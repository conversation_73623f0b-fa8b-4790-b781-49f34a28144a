"""
Pydantic models for the RAG Citation API.
"""
from datetime import datetime
from typing import Any, Dict, List, Optional, Union
from uuid import UUID, uuid4

from pydantic import BaseModel, Field


class DocumentMetadata(BaseModel):
    """Metadata for uploaded documents."""
    filename: str
    file_size: int
    content_type: str
    upload_timestamp: datetime
    chunk_count: Optional[int] = None


class Document(BaseModel):
    """Document model."""
    id: UUID = Field(default_factory=uuid4)
    user_id: UUID
    filename: str
    original_filename: str
    file_path: str  # Supabase Storage path
    storage_url: str  # Public URL from Supabase Storage
    metadata: DocumentMetadata
    status: str = "processing"  # processing, completed, failed
    error_message: Optional[str] = None


class DocumentChunk(BaseModel):
    """Document chunk model for vector storage."""
    id: str
    document_id: UUID
    chunk_index: int
    content: str
    metadata: Dict[str, Any]
    embedding: Optional[List[float]] = None


class Citation(BaseModel):
    """Citation model based on Anthropic's citation format."""
    type: str  # "char_location", "page_location", "content_block_location"
    cited_text: str
    document_index: int
    document_title: str
    start_char_index: Optional[int] = None
    end_char_index: Optional[int] = None
    start_page_number: Optional[int] = None
    end_page_number: Optional[int] = None
    start_block_index: Optional[int] = None
    end_block_index: Optional[int] = None


class TextBlock(BaseModel):
    """Text block with optional citations."""
    type: str = "text"
    text: str
    citations: Optional[List[Citation]] = None


class QueryRequest(BaseModel):
    """Request model for querying documents."""
    question: str = Field(..., min_length=1, max_length=1000)
    document_ids: Optional[List[UUID]] = None
    max_results: int = Field(default=5, ge=1, le=20)
    include_citations: bool = Field(default=True)


class QueryResponse(BaseModel):
    """Response model for document queries."""
    answer: str
    content_blocks: List[TextBlock]
    sources: List[Document]
    processing_time: float


class DocumentUploadResponse(BaseModel):
    """Response model for document upload."""
    document: Document
    message: str


class DocumentListResponse(BaseModel):
    """Response model for listing documents."""
    documents: List[Document]
    total_count: int


class ErrorResponse(BaseModel):
    """Error response model."""
    error: str
    detail: Optional[str] = None
    timestamp: datetime = Field(default_factory=datetime.utcnow)


class HealthResponse(BaseModel):
    """Health check response model."""
    status: str
    timestamp: datetime = Field(default_factory=datetime.utcnow)
    version: str
    services: Dict[str, str]


class User(BaseModel):
    """User model from Supabase Auth."""
    id: UUID
    email: str
    created_at: datetime
    updated_at: datetime


# Chat System Models

class Message(BaseModel):
    """Message model for chat conversations."""
    id: UUID = Field(default_factory=uuid4)
    conversation_id: UUID
    user_id: Optional[UUID] = Field(None, description="User who authored this message (null for AI/system messages)")
    sender_role: str = Field(..., description="Message sender role: user, ai, system")
    message_type: str = Field(default="text", description="Message type: text, document")
    content: str = Field(..., min_length=1, description="Message content text")
    reference_id: Optional[UUID] = Field(None, description="Optional reference to documents or other entities")
    created_at: datetime = Field(default_factory=datetime.utcnow)


class Conversation(BaseModel):
    """Conversation model for chat conversations."""
    id: UUID = Field(default_factory=uuid4)
    user_id: UUID
    title: Optional[str] = Field(None, max_length=255, description="Optional conversation title")
    created_at: datetime = Field(default_factory=datetime.utcnow)
    updated_at: datetime = Field(default_factory=datetime.utcnow)
    messages: List[Message] = Field(default_factory=list, description="Messages in the conversation")


class ConversationSummary(BaseModel):
    """Conversation summary without messages for list views."""
    id: UUID
    user_id: UUID
    title: Optional[str] = None
    created_at: datetime
    updated_at: datetime
    message_count: int = Field(0, description="Number of messages in the conversation")
    last_message_preview: Optional[str] = Field(None, description="Preview of the last message")


# Chat API Request/Response Models

class CreateConversationRequest(BaseModel):
    """Request model for creating a new conversation."""
    title: Optional[str] = Field(None, max_length=255, description="Optional conversation title")
    initial_message: Optional[str] = Field(None, min_length=1, max_length=10000, description="Optional initial message")


class SendMessageRequest(BaseModel):
    """Request model for sending a message in a conversation."""
    content: str = Field(..., min_length=1, max_length=10000, description="Message content")
    message_type: str = Field(default="text", description="Message type: text, document")
    reference_id: Optional[UUID] = Field(None, description="Optional reference to documents or other entities")


class ConversationResponse(BaseModel):
    """Response model for conversation operations."""
    conversation: Conversation
    message: str = Field(default="Success")


class ConversationListResponse(BaseModel):
    """Response model for listing conversations."""
    conversations: List[ConversationSummary]
    total_count: int
    page: int = Field(default=1, ge=1)
    page_size: int = Field(default=20, ge=1, le=100)


class MessageResponse(BaseModel):
    """Response model for message operations."""
    message: Message
    status: str = Field(default="success")
