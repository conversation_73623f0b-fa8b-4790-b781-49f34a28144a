"""
SQLAlchemy ORM models for the RAG Citation API.
"""
import uuid
from datetime import datetime
from typing import Optional

from sqlalchemy import BigInteger, DateTime, ForeignKey, Index, Integer, String, Text, func
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import Mapped, mapped_column, relationship

from app.database.base import Base, UUIDMixin


class Document(Base, UUIDMixin):
    """
    Document model for storing uploaded document metadata.

    This model represents documents uploaded by users with their metadata,
    storage information, and processing status. Each document is associated
    with a user through the user_id foreign key.

    Row Level Security (RLS) is enabled on this table in the database
    to ensure users can only access their own documents.
    """

    __tablename__ = "documents"

    # User association (references auth.users table in Supabase)
    user_id: Mapped[uuid.UUID] = mapped_column(
        UUID(as_uuid=True),
        nullable=False,
        comment="References auth.users(id) - the user who uploaded the document"
    )

    # File information
    filename: Mapped[str] = mapped_column(
        String(255),
        nullable=False,
        comment="Processed filename used for storage"
    )

    original_filename: Mapped[str] = mapped_column(
        String(255),
        nullable=False,
        comment="Original filename from upload"
    )

    file_path: Mapped[str] = mapped_column(
        Text,
        nullable=False,
        comment="Path to file in Supabase Storage"
    )

    storage_url: Mapped[str] = mapped_column(
        Text,
        nullable=False,
        comment="Public URL for accessing the file"
    )

    # File metadata
    file_size: Mapped[int] = mapped_column(
        BigInteger,
        nullable=False,
        comment="File size in bytes"
    )

    content_type: Mapped[str] = mapped_column(
        String(100),
        nullable=False,
        comment="MIME content type of the file"
    )

    # Processing information
    status: Mapped[str] = mapped_column(
        String(20),
        nullable=False,
        default="processing",
        server_default="processing",
        comment="Processing status: processing, completed, failed"
    )

    error_message: Mapped[Optional[str]] = mapped_column(
        Text,
        nullable=True,
        comment="Error message if processing failed"
    )

    chunk_count: Mapped[Optional[int]] = mapped_column(
        Integer,
        nullable=True,
        comment="Number of text chunks created from the document"
    )

    # Timestamp fields to match existing schema
    created_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True),
        server_default=func.now(),
        nullable=False,
        comment="Document upload timestamp"
    )

    updated_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True),
        server_default=func.now(),
        onupdate=func.now(),
        nullable=False,
        comment="Record last update timestamp"
    )

    # Indexes for performance
    __table_args__ = (
        # Index for user-specific document queries (most common query pattern)
        Index("idx_documents_user_id", "user_id"),

        # Index for sorting by upload timestamp
        Index("idx_documents_upload_timestamp", "created_at"),

        # Index for filtering by status
        Index("idx_documents_status", "status"),

        # Composite index for user + status queries
        Index("idx_documents_user_status", "user_id", "status"),

        # Composite index for user + timestamp (for pagination)
        Index("idx_documents_user_timestamp", "user_id", "created_at"),

        # Table comment
        {"comment": "Stores metadata for uploaded documents with user association"}
    )

    def __repr__(self) -> str:
        return f"<Document(id={self.id}, filename='{self.filename}', status='{self.status}')>"

    @property
    def is_processing(self) -> bool:
        """Check if document is currently being processed."""
        return self.status == "processing"

    @property
    def is_completed(self) -> bool:
        """Check if document processing is completed."""
        return self.status == "completed"

    @property
    def is_failed(self) -> bool:
        """Check if document processing failed."""
        return self.status == "failed"


class Conversation(Base, UUIDMixin):
    """
    Conversation model for storing chat conversations between users and AI.

    This model represents persistent conversations where users can have
    ongoing dialogues with the AI system. Each conversation is associated
    with a user through the user_id foreign key and contains multiple messages.

    Row Level Security (RLS) will be enabled on this table in the database
    to ensure users can only access their own conversations.
    """

    __tablename__ = "conversations"

    # User association (references auth.users table in Supabase)
    user_id: Mapped[uuid.UUID] = mapped_column(
        UUID(as_uuid=True),
        nullable=False,
        comment="References auth.users(id) - the user who owns the conversation"
    )

    # Conversation metadata
    title: Mapped[Optional[str]] = mapped_column(
        String(255),
        nullable=True,
        comment="Optional conversation title (can be auto-generated from first message)"
    )

    # Timestamp fields following Document model pattern
    created_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True),
        server_default=func.now(),
        nullable=False,
        comment="Conversation creation timestamp"
    )

    updated_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True),
        server_default=func.now(),
        onupdate=func.now(),
        nullable=False,
        comment="Conversation last update timestamp (updated when new messages are added)"
    )

    # Relationship to messages
    messages: Mapped[list["Message"]] = relationship(
        "Message",
        back_populates="conversation",
        cascade="all, delete-orphan",
        order_by="Message.created_at"
    )

    # Indexes for performance
    __table_args__ = (
        # Index for user-specific conversation queries (most common query pattern)
        Index("idx_conversations_user_id", "user_id"),

        # Index for sorting by creation timestamp
        Index("idx_conversations_created_at", "created_at"),

        # Index for sorting by last update timestamp
        Index("idx_conversations_updated_at", "updated_at"),

        # Composite index for user + creation timestamp (for pagination)
        Index("idx_conversations_user_created", "user_id", "created_at"),

        # Composite index for user + update timestamp (for recent conversations)
        Index("idx_conversations_user_updated", "user_id", "updated_at"),

        # Table comment
        {"comment": "Stores chat conversations between users and AI system"}
    )

    def __repr__(self) -> str:
        return f"<Conversation(id={self.id}, user_id={self.user_id}, title='{self.title}')>"


class Message(Base, UUIDMixin):
    """
    Message model for storing individual messages within conversations.

    This model represents individual messages exchanged between users and the AI
    system within a conversation. Messages are immutable once created and include
    metadata about the sender role and message type.

    Row Level Security (RLS) will be enabled on this table in the database
    to ensure users can only access messages from their own conversations.
    """

    __tablename__ = "messages"

    # Conversation association
    conversation_id: Mapped[uuid.UUID] = mapped_column(
        UUID(as_uuid=True),
        ForeignKey("conversations.id", ondelete="CASCADE"),
        nullable=False,
        comment="References conversations(id) - the conversation this message belongs to"
    )

    # User association (message author for RLS and performance)
    user_id: Mapped[Optional[uuid.UUID]] = mapped_column(
        UUID(as_uuid=True),
        nullable=True,
        comment="References auth.users(id) - the user who authored this message (null for AI/system messages)"
    )

    # Message metadata
    sender_role: Mapped[str] = mapped_column(
        String(20),
        nullable=False,
        comment="Message sender role: user, ai, system"
    )

    message_type: Mapped[str] = mapped_column(
        String(20),
        nullable=False,
        default="text",
        server_default="text",
        comment="Message type: text, document (for future document attachments)"
    )

    # Message content
    content: Mapped[str] = mapped_column(
        Text,
        nullable=False,
        comment="Message content text"
    )

    # Optional reference to other entities (e.g., documents)
    reference_id: Mapped[Optional[uuid.UUID]] = mapped_column(
        UUID(as_uuid=True),
        nullable=True,
        comment="Optional reference to documents or other entities"
    )

    # Timestamp field (messages are immutable, so no updated_at)
    created_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True),
        server_default=func.now(),
        nullable=False,
        comment="Message creation timestamp"
    )

    # Relationship to conversation
    conversation: Mapped["Conversation"] = relationship(
        "Conversation",
        back_populates="messages"
    )

    # Indexes for performance
    __table_args__ = (
        # Index for conversation-specific message queries (most common query pattern)
        Index("idx_messages_conversation_id", "conversation_id"),

        # Index for user-specific message queries (critical for RLS performance)
        Index("idx_messages_user_id", "user_id"),

        # Index for sorting messages by timestamp within conversations
        Index("idx_messages_created_at", "created_at"),

        # Composite index for conversation + timestamp (for message ordering)
        Index("idx_messages_conversation_created", "conversation_id", "created_at"),

        # Composite index for user + timestamp (for user message history)
        Index("idx_messages_user_created", "user_id", "created_at"),

        # Index for filtering by sender role
        Index("idx_messages_sender_role", "sender_role"),

        # Index for filtering by message type
        Index("idx_messages_message_type", "message_type"),

        # Index for reference lookups (when messages reference documents)
        Index("idx_messages_reference_id", "reference_id"),

        # Composite index for user + conversation (for user messages in specific conversations)
        Index("idx_messages_user_conversation", "user_id", "conversation_id"),

        # Table comment
        {"comment": "Stores individual messages within conversations"}
    )

    def __repr__(self) -> str:
        content_preview = self.content[:50] + "..." if len(self.content) > 50 else self.content
        return f"<Message(id={self.id}, conversation_id={self.conversation_id}, sender_role='{self.sender_role}', content='{content_preview}')>"

    @property
    def is_user_message(self) -> bool:
        """Check if message is from user."""
        return self.sender_role == "user"

    @property
    def is_ai_message(self) -> bool:
        """Check if message is from AI."""
        return self.sender_role == "ai"

    @property
    def is_system_message(self) -> bool:
        """Check if message is a system message."""
        return self.sender_role == "system"

    @property
    def is_text_message(self) -> bool:
        """Check if message is a text message."""
        return self.message_type == "text"

    @property
    def is_document_message(self) -> bool:
        """Check if message references a document."""
        return self.message_type == "document"


# Note: The auth.users table is managed by Supabase Auth and is not defined here.
# It exists in the 'auth' schema and is referenced by the user_id foreign key.
#
# Row Level Security (RLS) policies are applied at the database level:
# - Users can only insert documents with their own user_id
# - Users can only select their own documents
# - Users can only update their own documents
# - Users can only delete their own documents
# - Service role can bypass RLS for admin operations
#
# These policies are defined in the migration files and applied directly
# to the database, not through SQLAlchemy.
#
# Similar RLS policies will be applied to conversations and messages tables:
# - Users can only access conversations they own
# - Users can only access messages they authored (user_id = auth.uid()) or from their conversations
# - The user_id field in messages enables efficient RLS policies and tracks message authorship
# - For AI/system messages, user_id is null and access is controlled via conversation ownership
# - Cascade delete ensures message cleanup when conversations are deleted
#
# Design Decision: user_id in messages table
# While this creates some data redundancy (violates 3NF), it provides significant benefits:
# 1. Massive RLS performance improvement (direct index lookup vs subquery)
# 2. Efficient user message queries across all conversations
# 3. Message authorship tracking for future shared conversation features
# 4. Consistency with existing codebase patterns (documents table)
