"""Add conversation and message models with user_id for chat system

Revision ID: fdfb24d904a2
Revises: 4ca3f0f7efac
Create Date: 2025-05-25 19:31:47.461439

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'fdfb24d904a2'
down_revision: Union[str, None] = '4ca3f0f7efac'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('conversations',
    sa.Column('user_id', sa.UUID(), nullable=False, comment='References auth.users(id) - the user who owns the conversation'),
    sa.Column('title', sa.String(length=255), nullable=True, comment='Optional conversation title (can be auto-generated from first message)'),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False, comment='Conversation creation timestamp'),
    sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False, comment='Conversation last update timestamp (updated when new messages are added)'),
    sa.Column('id', sa.UUID(), server_default=sa.text('gen_random_uuid()'), nullable=False, comment='Primary key UUID'),
    sa.PrimaryKeyConstraint('id', name=op.f('pk_conversations')),
    comment='Stores chat conversations between users and AI system'
    )
    op.create_index('idx_conversations_created_at', 'conversations', ['created_at'], unique=False)
    op.create_index('idx_conversations_updated_at', 'conversations', ['updated_at'], unique=False)
    op.create_index('idx_conversations_user_created', 'conversations', ['user_id', 'created_at'], unique=False)
    op.create_index('idx_conversations_user_id', 'conversations', ['user_id'], unique=False)
    op.create_index('idx_conversations_user_updated', 'conversations', ['user_id', 'updated_at'], unique=False)
    op.create_table('messages',
    sa.Column('conversation_id', sa.UUID(), nullable=False, comment='References conversations(id) - the conversation this message belongs to'),
    sa.Column('user_id', sa.UUID(), nullable=True, comment='References auth.users(id) - the user who authored this message (null for AI/system messages)'),
    sa.Column('sender_role', sa.String(length=20), nullable=False, comment='Message sender role: user, ai, system'),
    sa.Column('message_type', sa.String(length=20), server_default='text', nullable=False, comment='Message type: text, document (for future document attachments)'),
    sa.Column('content', sa.Text(), nullable=False, comment='Message content text'),
    sa.Column('reference_id', sa.UUID(), nullable=True, comment='Optional reference to documents or other entities'),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False, comment='Message creation timestamp'),
    sa.Column('id', sa.UUID(), server_default=sa.text('gen_random_uuid()'), nullable=False, comment='Primary key UUID'),
    sa.ForeignKeyConstraint(['conversation_id'], ['conversations.id'], name=op.f('fk_messages_conversation_id_conversations'), ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('id', name=op.f('pk_messages')),
    comment='Stores individual messages within conversations'
    )
    op.create_index('idx_messages_conversation_created', 'messages', ['conversation_id', 'created_at'], unique=False)
    op.create_index('idx_messages_conversation_id', 'messages', ['conversation_id'], unique=False)
    op.create_index('idx_messages_created_at', 'messages', ['created_at'], unique=False)
    op.create_index('idx_messages_message_type', 'messages', ['message_type'], unique=False)
    op.create_index('idx_messages_reference_id', 'messages', ['reference_id'], unique=False)
    op.create_index('idx_messages_sender_role', 'messages', ['sender_role'], unique=False)
    op.create_index('idx_messages_user_conversation', 'messages', ['user_id', 'conversation_id'], unique=False)
    op.create_index('idx_messages_user_created', 'messages', ['user_id', 'created_at'], unique=False)
    op.create_index('idx_messages_user_id', 'messages', ['user_id'], unique=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index('idx_messages_user_id', table_name='messages')
    op.drop_index('idx_messages_user_created', table_name='messages')
    op.drop_index('idx_messages_user_conversation', table_name='messages')
    op.drop_index('idx_messages_sender_role', table_name='messages')
    op.drop_index('idx_messages_reference_id', table_name='messages')
    op.drop_index('idx_messages_message_type', table_name='messages')
    op.drop_index('idx_messages_created_at', table_name='messages')
    op.drop_index('idx_messages_conversation_id', table_name='messages')
    op.drop_index('idx_messages_conversation_created', table_name='messages')
    op.drop_table('messages')
    op.drop_index('idx_conversations_user_updated', table_name='conversations')
    op.drop_index('idx_conversations_user_id', table_name='conversations')
    op.drop_index('idx_conversations_user_created', table_name='conversations')
    op.drop_index('idx_conversations_updated_at', table_name='conversations')
    op.drop_index('idx_conversations_created_at', table_name='conversations')
    op.drop_table('conversations')
    # ### end Alembic commands ###
